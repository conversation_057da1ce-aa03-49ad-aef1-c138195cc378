<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Configuración del Módulo de Gestión de Personas
    |--------------------------------------------------------------------------
    |
    | Este archivo contiene la configuración para el módulo de gestión de
    | personas, incluyendo límites, validaciones y opciones del sistema.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Límites del Sistema 1x10
    |--------------------------------------------------------------------------
    |
    | Configuración para la metodología 1x10 donde un líder puede gestionar
    | hasta un número específico de personas.
    |
    */
    'lider_1x10' => [
        'max_personas_asignadas' => 10,
        'permitir_auto_asignacion' => false,
        'notificar_limite_alcanzado' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Validaciones de Datos
    |--------------------------------------------------------------------------
    |
    | Configuración para las validaciones de datos de personas.
    |
    */
    'validaciones' => [
        'cedula' => [
            'requerida' => true,
            'unica' => true,
            'formato' => '/^[VEJ]-\d{7,8}$/', // Formato venezolano
        ],
        'email' => [
            'requerido' => false,
            'unico' => true,
        ],
        'telefono' => [
            'requerido' => false,
            'formato' => '/^0\d{3}-\d{7}$/', // Formato venezolano
        ],
        'fecha_nacimiento' => [
            'edad_minima' => 16,
            'edad_maxima' => 120,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Opciones de Exportación
    |--------------------------------------------------------------------------
    |
    | Configuración para las funcionalidades de exportación de datos.
    |
    */
    'exportacion' => [
        'formatos_permitidos' => ['excel', 'csv', 'pdf'],
        'limite_registros' => 10000,
        'incluir_datos_sensibles' => false,
        'campos_exportables' => [
            'nombres',
            'apellidos',
            'cedula',
            'telefono',
            'email',
            'tipo_persona',
            'estado',
            'ubicacion_completa',
            'es_lider_1x10',
            'fecha_registro',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Opciones de Importación
    |--------------------------------------------------------------------------
    |
    | Configuración para las funcionalidades de importación de datos.
    |
    */
    'importacion' => [
        'formatos_permitidos' => ['excel', 'csv'],
        'tamaño_maximo_archivo' => '10MB',
        'limite_registros_por_lote' => 1000,
        'validar_duplicados' => true,
        'crear_ubicaciones_faltantes' => false,
        'campos_requeridos' => [
            'nombres',
            'apellidos',
            'cedula',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuración de Búsqueda
    |--------------------------------------------------------------------------
    |
    | Opciones para las funcionalidades de búsqueda y filtrado.
    |
    */
    'busqueda' => [
        'resultados_por_pagina' => [10, 15, 25, 50, 100],
        'busqueda_minima_caracteres' => 2,
        'incluir_busqueda_fuzzy' => false,
        'campos_busqueda_rapida' => [
            'nombres',
            'apellidos',
            'cedula',
            'email',
            'telefono',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notificaciones
    |--------------------------------------------------------------------------
    |
    | Configuración para el sistema de notificaciones del módulo.
    |
    */
    'notificaciones' => [
        'enviar_email_nuevo_usuario' => true,
        'enviar_email_asignacion_lider' => true,
        'notificar_cambios_importantes' => true,
        'plantillas_email' => [
            'nuevo_usuario' => 'emails.personas.nuevo-usuario',
            'asignacion_lider' => 'emails.personas.asignacion-lider',
            'cambio_datos' => 'emails.personas.cambio-datos',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuración de Seguridad
    |--------------------------------------------------------------------------
    |
    | Opciones de seguridad para el módulo de personas.
    |
    */
    'seguridad' => [
        'log_cambios_importantes' => true,
        'requerir_confirmacion_eliminacion' => true,
        'permitir_eliminacion_con_asignados' => false,
        'encriptar_datos_sensibles' => false,
        'auditoria_completa' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuración de Ubicaciones
    |--------------------------------------------------------------------------
    |
    | Opciones para el manejo de ubicaciones geográficas.
    |
    */
    'ubicaciones' => [
        'requerir_ubicacion_completa' => false,
        'validar_jerarquia_ubicacion' => true,
        'permitir_crear_ubicaciones' => false,
        'cache_ubicaciones' => true,
        'cache_duracion' => 3600, // 1 hora en segundos
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuración de Roles
    |--------------------------------------------------------------------------
    |
    | Mapeo de tipos de persona a roles del sistema.
    |
    */
    'roles' => [
        'mapeo_automatico' => true,
        'mapeo_tipos' => [
            'militante' => 'Militante',
            'votante' => 'Votante',
            'simpatizante' => 'Votante', // Los simpatizantes usan el rol de votante
        ],
        'rol_lider_1x10' => 'Líder 1x10',
        'rol_por_defecto' => 'Votante',
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuración de Dashboard
    |--------------------------------------------------------------------------
    |
    | Opciones para las métricas y estadísticas del dashboard.
    |
    */
    'dashboard' => [
        'mostrar_estadisticas' => true,
        'cache_estadisticas' => true,
        'cache_duracion_estadisticas' => 1800, // 30 minutos
        'metricas_principales' => [
            'total_personas',
            'total_militantes',
            'total_votantes',
            'total_lideres_1x10',
            'personas_sin_lider',
            'lideres_con_espacios',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuración de API
    |--------------------------------------------------------------------------
    |
    | Opciones para la API REST del módulo (futuro).
    |
    */
    'api' => [
        'habilitada' => false,
        'version' => 'v1',
        'rate_limit' => '60:1', // 60 requests por minuto
        'requerir_autenticacion' => true,
        'endpoints_publicos' => [],
    ],
];
