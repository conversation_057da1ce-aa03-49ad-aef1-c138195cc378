<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CentroVotacion extends Model
{
    use HasFactory;

    protected $table = 'centros_votacion';

    protected $fillable = [
        'parroquia_id',
        'nombre',
        'codigo',
        'direccion',
        'activo',
    ];

    protected $casts = [
        'activo' => 'boolean',
    ];

    /**
     * Relación con parroquia
     */
    public function parroquia(): BelongsTo
    {
        return $this->belongsTo(Parroquia::class);
    }

    /**
     * Relación con personas
     */
    public function personas(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Persona::class);
    }

    /**
     * Scope para centros activos
     */
    public function scopeActivos($query)
    {
        return $query->where('activo', true);
    }
}
