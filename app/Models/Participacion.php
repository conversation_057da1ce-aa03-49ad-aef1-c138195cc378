<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Participacion extends Model
{
    use HasFactory;

    protected $table = 'participaciones';

    protected $fillable = [
        'persona_id',
        'evento_electoral_id',
        'tipo_participacion',
        'confirmo_participacion',
        'fecha_confirmacion',
        'observaciones',
    ];

    protected $casts = [
        'confirmo_participacion' => 'boolean',
        'fecha_confirmacion' => 'datetime',
    ];

    /**
     * Relación con persona
     */
    public function persona(): BelongsTo
    {
        return $this->belongsTo(Persona::class);
    }

    /**
     * Relación con evento electoral
     */
    public function eventoElectoral(): BelongsTo
    {
        return $this->belongsTo(EventoElectoral::class);
    }
}
