<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class EventoElectoral extends Model
{
    use HasFactory;

    protected $table = 'eventos_electorales';

    protected $fillable = [
        'nombre',
        'descripcion',
        'fecha_evento',
        'tipo',
        'estado',
    ];

    protected $casts = [
        'fecha_evento' => 'date',
    ];

    /**
     * Relación con participaciones
     */
    public function participaciones(): HasMany
    {
        return $this->hasMany(Participacion::class);
    }

    /**
     * Relación con personas
     */
    public function personas(): BelongsToMany
    {
        return $this->belongsToMany(Persona::class, 'participaciones')
            ->withPivot(['tipo_participacion', 'confirmo_participacion', 'fecha_confirmacion', 'observaciones'])
            ->withTimestamps();
    }

    /**
     * Scope para eventos activos
     */
    public function scopeActivos($query)
    {
        return $query->whereIn('estado', ['programado', 'en_curso']);
    }
}
