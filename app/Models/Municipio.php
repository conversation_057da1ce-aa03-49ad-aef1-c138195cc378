<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Municipio extends Model
{
    use HasFactory;

    protected $table = 'municipios';

    protected $fillable = [
        'estado_id',
        'nombre',
        'codigo',
        'activo',
    ];

    protected $casts = [
        'activo' => 'boolean',
    ];

    /**
     * Relación con estado
     */
    public function estado(): BelongsTo
    {
        return $this->belongsTo(Estado::class);
    }

    /**
     * Relación con parroquias
     */
    public function parroquias(): HasMany
    {
        return $this->hasMany(Parroquia::class);
    }

    /**
     * Relación con personas
     */
    public function personas(): HasM<PERSON>
    {
        return $this->hasMany(Persona::class);
    }

    /**
     * Scope para municipios activos
     */
    public function scopeActivos($query)
    {
        return $query->where('activo', true);
    }
}
