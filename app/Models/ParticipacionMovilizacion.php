<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ParticipacionMovilizacion extends Model
{
    use HasFactory;

    protected $table = 'participaciones_movilizacion';

    protected $fillable = [
        'persona_id',
        'movilizacion_id',
        'estado_participacion',
        'fecha_confirmacion',
        'fecha_asistencia',
        'observaciones',
    ];

    protected $casts = [
        'fecha_confirmacion' => 'datetime',
        'fecha_asistencia' => 'datetime',
    ];

    /**
     * Relación con persona
     */
    public function persona(): BelongsTo
    {
        return $this->belongsTo(Persona::class);
    }

    /**
     * Relación con movilización
     */
    public function movilizacion(): BelongsTo
    {
        return $this->belongsTo(Movilizacion::class);
    }
}
