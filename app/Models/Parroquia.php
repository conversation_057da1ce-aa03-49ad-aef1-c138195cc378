<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Parroquia extends Model
{
    use HasFactory;

    protected $table = 'parroquias';

    protected $fillable = [
        'municipio_id',
        'nombre',
        'codigo',
        'activo',
    ];

    protected $casts = [
        'activo' => 'boolean',
    ];

    /**
     * Relación con municipio
     */
    public function municipio(): BelongsTo
    {
        return $this->belongsTo(Municipio::class);
    }

    /**
     * Relación con centros de votación
     */
    public function centrosVotacion(): Has<PERSON><PERSON>
    {
        return $this->hasMany(CentroVotacion::class);
    }

    /**
     * Relación con personas
     */
    public function personas(): HasMany
    {
        return $this->hasMany(Persona::class);
    }

    /**
     * Scope para parroquias activas
     */
    public function scopeActivos($query)
    {
        return $query->where('activo', true);
    }
}
