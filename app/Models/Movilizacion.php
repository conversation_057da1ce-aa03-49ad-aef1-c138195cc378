<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Movilizacion extends Model
{
    use HasFactory;

    protected $table = 'movilizaciones';

    protected $fillable = [
        'nombre',
        'descripcion',
        'fecha_inicio',
        'fecha_fin',
        'tipo',
        'estado',
        'ubicacion',
        'meta_participantes',
    ];

    protected $casts = [
        'fecha_inicio' => 'datetime',
        'fecha_fin' => 'datetime',
    ];

    /**
     * Relación con participaciones
     */
    public function participacionesMovilizacion(): HasMany
    {
        return $this->hasMany(ParticipacionMovilizacion::class);
    }

    /**
     * Relación con personas
     */
    public function personas(): BelongsToMany
    {
        return $this->belongsToMany(Persona::class, 'participaciones_movilizacion')
            ->withPivot(['estado_participacion', 'fecha_confirmacion', 'fecha_asistencia', 'observaciones'])
            ->withTimestamps();
    }

    /**
     * Scope para movilizaciones activas
     */
    public function scopeActivas($query)
    {
        return $query->whereIn('estado', ['planificada', 'en_curso']);
    }
}
