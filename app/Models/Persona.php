<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Persona extends Model
{
    use HasFactory;

    protected $table = 'personas';

    protected $fillable = [
        'nombres',
        'apellidos',
        'cedula',
        'fecha_nacimiento',
        'genero',
        'telefono',
        'telefono_secundario',
        'email',
        'direccion',
        'estado_id',
        'municipio_id',
        'parroquia_id',
        'centro_votacion_id',
        'mesa_votacion',
        'tipo_persona',
        'es_lider_1x10',
        'lider_asignado_id',
        'user_id',
        'estado',
        'observaciones',
        'datos_adicionales',
    ];

    protected $casts = [
        'fecha_nacimiento' => 'date',
        'es_lider_1x10' => 'boolean',
        'datos_adicionales' => 'array',
    ];

    /**
     * Relación con estado
     */
    public function estado(): BelongsTo
    {
        return $this->belongsTo(Estado::class);
    }

    /**
     * Relación con municipio
     */
    public function municipio(): BelongsTo
    {
        return $this->belongsTo(Municipio::class);
    }

    /**
     * Relación con parroquia
     */
    public function parroquia(): BelongsTo
    {
        return $this->belongsTo(Parroquia::class);
    }

    /**
     * Relación con centro de votación
     */
    public function centroVotacion(): BelongsTo
    {
        return $this->belongsTo(CentroVotacion::class);
    }

    /**
     * Relación con usuario del sistema
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relación con líder asignado
     */
    public function liderAsignado(): BelongsTo
    {
        return $this->belongsTo(Persona::class, 'lider_asignado_id');
    }

    /**
     * Relación con personas asignadas (si es líder)
     */
    public function personasAsignadas(): HasMany
    {
        return $this->hasMany(Persona::class, 'lider_asignado_id');
    }

    /**
     * Relación con participaciones en eventos electorales
     */
    public function participaciones(): HasMany
    {
        return $this->hasMany(Participacion::class);
    }

    /**
     * Relación con participaciones en movilizaciones
     */
    public function participacionesMovilizacion(): HasMany
    {
        return $this->hasMany(ParticipacionMovilizacion::class);
    }

    /**
     * Relación con eventos electorales
     */
    public function eventosElectorales(): BelongsToMany
    {
        return $this->belongsToMany(EventoElectoral::class, 'participaciones')
            ->withPivot(['tipo_participacion', 'confirmo_participacion', 'fecha_confirmacion', 'observaciones'])
            ->withTimestamps();
    }

    /**
     * Relación con movilizaciones
     */
    public function movilizaciones(): BelongsToMany
    {
        return $this->belongsToMany(Movilizacion::class, 'participaciones_movilizacion')
            ->withPivot(['estado_participacion', 'fecha_confirmacion', 'fecha_asistencia', 'observaciones'])
            ->withTimestamps();
    }

    /**
     * Accessor para nombre completo
     */
    public function getNombreCompletoAttribute(): string
    {
        return trim($this->nombres . ' ' . $this->apellidos);
    }

    /**
     * Accessor para edad
     */
    public function getEdadAttribute(): ?int
    {
        return $this->fecha_nacimiento ? $this->fecha_nacimiento->age : null;
    }

    /**
     * Scope para personas activas
     */
    public function scopeActivas($query)
    {
        return $query->where('estado', 'activo');
    }

    /**
     * Scope para militantes
     */
    public function scopeMilitantes($query)
    {
        return $query->where('tipo_persona', 'militante');
    }

    /**
     * Scope para votantes
     */
    public function scopeVotantes($query)
    {
        return $query->where('tipo_persona', 'votante');
    }

    /**
     * Scope para líderes 1x10
     */
    public function scopeLideres1x10($query)
    {
        return $query->where('es_lider_1x10', true);
    }

    /**
     * Scope para búsqueda por texto
     */
    public function scopeBuscarTexto($query, $texto)
    {
        return $query->where(function ($q) use ($texto) {
            $q->where('nombres', 'like', "%{$texto}%")
              ->orWhere('apellidos', 'like', "%{$texto}%")
              ->orWhere('cedula', 'like', "%{$texto}%")
              ->orWhere('email', 'like', "%{$texto}%")
              ->orWhere('telefono', 'like', "%{$texto}%");
        });
    }

    /**
     * Verificar si puede ser líder 1x10
     */
    public function puedeSerLider1x10(): bool
    {
        return $this->personasAsignadas()->count() < 10;
    }

    /**
     * Obtener espacios disponibles como líder
     */
    public function espaciosDisponiblesLider(): int
    {
        return 10 - $this->personasAsignadas()->count();
    }
}
