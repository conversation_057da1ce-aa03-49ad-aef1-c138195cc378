<?php

namespace App\Livewire\Admin\Personas;

use App\Models\Persona;
use App\Models\Estado;
use App\Models\Municipio;
use App\Models\Parroquia;
use App\Models\CentroVotacion;
use Illuminate\Contracts\View\View;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Validate;
use Livewire\Component;

class EditPersona extends Component
{
    use LivewireAlert;

    public Persona $persona;

    // Datos personales
    #[Validate('required|string|max:255')]
    public string $nombres = '';

    #[Validate('required|string|max:255')]
    public string $apellidos = '';

    #[Validate('required|string|max:20')]
    public string $cedula = '';

    #[Validate('nullable|date|before:today')]
    public ?string $fecha_nacimiento = null;

    #[Validate('nullable|in:M,F,O')]
    public ?string $genero = null;

    // Información de contacto
    #[Validate('nullable|string|max:20')]
    public ?string $telefono = null;

    #[Validate('nullable|string|max:20')]
    public ?string $telefono_secundario = null;

    #[Validate('nullable|email|max:255')]
    public ?string $email = null;

    #[Validate('nullable|string|max:500')]
    public ?string $direccion = null;

    // Ubicación
    #[Validate('nullable|exists:estados,id')]
    public ?int $estado_id = null;

    #[Validate('nullable|exists:municipios,id')]
    public ?int $municipio_id = null;

    #[Validate('nullable|exists:parroquias,id')]
    public ?int $parroquia_id = null;

    // Información electoral
    #[Validate('nullable|exists:centros_votacion,id')]
    public ?int $centro_votacion_id = null;

    #[Validate('nullable|string|max:10')]
    public ?string $mesa_votacion = null;

    // Rol en el sistema
    #[Validate('required|in:militante,votante,simpatizante')]
    public string $tipo_persona = 'votante';

    #[Validate('boolean')]
    public bool $es_lider_1x10 = false;

    #[Validate('nullable|exists:personas,id')]
    public ?int $lider_asignado_id = null;

    // Estado y observaciones
    #[Validate('required|in:activo,inactivo,suspendido')]
    public string $estado = 'activo';

    #[Validate('nullable|string|max:1000')]
    public ?string $observaciones = null;

    public function mount(Persona $persona): void
    {
        $this->authorize('update personas');
        
        $this->persona = $persona;
        
        // Cargar datos existentes
        $this->nombres = $persona->nombres;
        $this->apellidos = $persona->apellidos;
        $this->cedula = $persona->cedula;
        $this->fecha_nacimiento = $persona->fecha_nacimiento?->format('Y-m-d');
        $this->genero = $persona->genero;
        $this->telefono = $persona->telefono;
        $this->telefono_secundario = $persona->telefono_secundario;
        $this->email = $persona->email;
        $this->direccion = $persona->direccion;
        $this->estado_id = $persona->estado_id;
        $this->municipio_id = $persona->municipio_id;
        $this->parroquia_id = $persona->parroquia_id;
        $this->centro_votacion_id = $persona->centro_votacion_id;
        $this->mesa_votacion = $persona->mesa_votacion;
        $this->tipo_persona = $persona->tipo_persona;
        $this->es_lider_1x10 = $persona->es_lider_1x10;
        $this->lider_asignado_id = $persona->lider_asignado_id;
        $this->estado = $persona->estado;
        $this->observaciones = $persona->observaciones;
    }

    public function updatedEstadoId(): void
    {
        $this->municipio_id = null;
        $this->parroquia_id = null;
        $this->centro_votacion_id = null;
    }

    public function updatedMunicipioId(): void
    {
        $this->parroquia_id = null;
        $this->centro_votacion_id = null;
    }

    public function updatedParroquiaId(): void
    {
        $this->centro_votacion_id = null;
    }

    public function updatedCedula(): void
    {
        // Verificar si ya existe otra persona con esta cédula
        if ($this->cedula && Persona::where('cedula', $this->cedula)->where('id', '!=', $this->persona->id)->exists()) {
            $this->addError('cedula', __('personas.cedula_ya_existe'));
        }
    }

    public function updatedEmail(): void
    {
        // Verificar si ya existe otra persona con este email
        if ($this->email && Persona::where('email', $this->email)->where('id', '!=', $this->persona->id)->exists()) {
            $this->addError('email', __('personas.email_ya_existe'));
        }
    }

    public function updatedLiderAsignadoId(): void
    {
        if ($this->lider_asignado_id) {
            // No puede ser líder de sí mismo
            if ($this->lider_asignado_id == $this->persona->id) {
                $this->addError('lider_asignado_id', __('personas.no_puede_ser_lider_propio'));
                $this->lider_asignado_id = null;
                return;
            }

            $lider = Persona::find($this->lider_asignado_id);
            if ($lider && !$lider->puedeSerLider1x10()) {
                $this->addError('lider_asignado_id', __('personas.lider_sin_espacios'));
                $this->lider_asignado_id = null;
            }
        }
    }

    public function updatedEsLider1x10(): void
    {
        // Si se marca como líder, no puede tener líder asignado
        if ($this->es_lider_1x10) {
            $this->lider_asignado_id = null;
        }
    }

    public function updatePersona(): void
    {
        $this->authorize('update personas');

        // Validar reglas de cédula única
        $this->validate([
            'cedula' => 'required|string|max:20|unique:personas,cedula,' . $this->persona->id,
            'email' => 'nullable|email|max:255|unique:personas,email,' . $this->persona->id,
        ]);

        $this->validate();

        // Validaciones adicionales
        if ($this->lider_asignado_id) {
            if ($this->lider_asignado_id == $this->persona->id) {
                $this->addError('lider_asignado_id', __('personas.no_puede_ser_lider_propio'));
                return;
            }

            $lider = Persona::find($this->lider_asignado_id);
            if (!$lider || !$lider->puedeSerLider1x10()) {
                $this->addError('lider_asignado_id', __('personas.lider_sin_espacios'));
                return;
            }
        }

        // Si se está quitando el rol de líder, verificar que no tenga personas asignadas
        if (!$this->es_lider_1x10 && $this->persona->es_lider_1x10 && $this->persona->personasAsignadas()->count() > 0) {
            $this->addError('es_lider_1x10', 'No se puede quitar el rol de líder mientras tenga personas asignadas');
            return;
        }

        try {
            $this->persona->update([
                'nombres' => $this->nombres,
                'apellidos' => $this->apellidos,
                'cedula' => $this->cedula,
                'fecha_nacimiento' => $this->fecha_nacimiento,
                'genero' => $this->genero,
                'telefono' => $this->telefono,
                'telefono_secundario' => $this->telefono_secundario,
                'email' => $this->email,
                'direccion' => $this->direccion,
                'estado_id' => $this->estado_id,
                'municipio_id' => $this->municipio_id,
                'parroquia_id' => $this->parroquia_id,
                'centro_votacion_id' => $this->centro_votacion_id,
                'mesa_votacion' => $this->mesa_votacion,
                'tipo_persona' => $this->tipo_persona,
                'es_lider_1x10' => $this->es_lider_1x10,
                'lider_asignado_id' => $this->es_lider_1x10 ? null : $this->lider_asignado_id,
                'estado' => $this->estado,
                'observaciones' => $this->observaciones,
            ]);

            $this->flash('success', __('personas.persona_actualizada'));
            $this->redirect(route('admin.personas.show', $this->persona), navigate: true);

        } catch (\Exception $e) {
            $this->alert('error', __('personas.error_actualizar_persona'));
        }
    }

    public function getMunicipiosProperty()
    {
        if (!$this->estado_id) {
            return collect();
        }

        return Municipio::where('estado_id', $this->estado_id)
            ->activos()
            ->orderBy('nombre')
            ->get();
    }

    public function getParroquiasProperty()
    {
        if (!$this->municipio_id) {
            return collect();
        }

        return Parroquia::where('municipio_id', $this->municipio_id)
            ->activos()
            ->orderBy('nombre')
            ->get();
    }

    public function getCentrosVotacionProperty()
    {
        if (!$this->parroquia_id) {
            return collect();
        }

        return CentroVotacion::where('parroquia_id', $this->parroquia_id)
            ->activos()
            ->orderBy('nombre')
            ->get();
    }

    public function getLideresDisponiblesProperty()
    {
        return Persona::where('es_lider_1x10', true)
            ->where('estado', 'activo')
            ->where('id', '!=', $this->persona->id) // Excluir la persona actual
            ->whereHas('personasAsignadas', function ($query) {
                $query->havingRaw('COUNT(*) < 10');
            }, '<', 10)
            ->orWhere(function ($query) {
                $query->where('es_lider_1x10', true)
                    ->where('estado', 'activo')
                    ->where('id', '!=', $this->persona->id)
                    ->doesntHave('personasAsignadas');
            })
            ->orderBy('nombres')
            ->orderBy('apellidos')
            ->get();
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.personas.edit-persona', [
            'estados' => Estado::activos()->orderBy('nombre')->get(),
            'municipios' => $this->municipios,
            'parroquias' => $this->parroquias,
            'centrosVotacion' => $this->centrosVotacion,
            'lideresDisponibles' => $this->lideresDisponibles,
        ]);
    }
}
