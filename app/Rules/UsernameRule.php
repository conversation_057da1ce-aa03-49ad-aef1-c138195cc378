<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class UsernameRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Username debe tener entre 3 y 20 caracteres
        if (strlen($value) < 3 || strlen($value) > 20) {
            $fail('El :attribute debe tener entre 3 y 20 caracteres.');
            return;
        }

        // Username solo puede contener letras, números, guiones y guiones bajos
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $value)) {
            $fail('El :attribute solo puede contener letras, números, guiones y guiones bajos.');
            return;
        }

        // Username no puede empezar con número
        if (preg_match('/^[0-9]/', $value)) {
            $fail('El :attribute no puede empezar con un número.');
            return;
        }
    }
}
