<?php

namespace App\Console\Commands;

use App\Models\Persona;
use App\Models\Estado;
use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;

class TestPersonasModule extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:personas-module';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Prueba el funcionamiento del módulo de gestión de personas';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Probando el módulo de Gestión de Personas...');
        $this->newLine();

        // Verificar estructura de base de datos
        $this->info('📊 Verificando estructura de base de datos:');
        $this->checkTable('estados', Estado::count());
        $this->checkTable('personas', Persona::count());

        // Verificar roles y permisos
        $this->info('🔐 Verificando roles y permisos:');
        $roles = Role::with('permissions')->whereIn('name', [
            'Super Admin',
            'Coordinador de Personas',
            'Líder 1x10',
            'Militante',
            'Votante',
            'Operador de Datos'
        ])->get();

        foreach ($roles as $role) {
            $this->line("  ✅ Rol: {$role->name} ({$role->permissions->count()} permisos)");
        }

        // Verificar personas de ejemplo
        $this->info('👥 Verificando personas de ejemplo:');
        $lideres = Persona::where('es_lider_1x10', true)->get();
        $this->line("  ✅ Líderes 1x10: {$lideres->count()}");

        foreach ($lideres as $lider) {
            $asignadas = $lider->personasAsignadas->count();
            $this->line("    - {$lider->nombre_completo}: {$asignadas}/10 personas asignadas");
        }

        $totalPersonas = Persona::count();
        $militantes = Persona::militantes()->count();
        $votantes = Persona::votantes()->count();
        $simpatizantes = Persona::where('tipo_persona', 'simpatizante')->count();

        $this->line("  ✅ Total personas: {$totalPersonas}");
        $this->line("    - Militantes: {$militantes}");
        $this->line("    - Votantes: {$votantes}");
        $this->line("    - Simpatizantes: {$simpatizantes}");

        // Verificar usuarios asociados
        $this->info('👤 Verificando usuarios del sistema:');
        $personasConUsuario = Persona::whereNotNull('user_id')->count();
        $this->line("  ✅ Personas con usuario: {$personasConUsuario}");

        // Verificar rutas
        $this->info('🌐 Rutas disponibles:');
        $this->line('  ✅ /admin/personas - Listado de personas');
        $this->line('  ✅ /admin/personas/create - Crear persona');
        $this->line('  ✅ /admin/personas/busqueda-avanzada - Búsqueda avanzada');
        $this->line('  ✅ /admin/personas/{id} - Ver detalles');
        $this->line('  ✅ /admin/personas/{id}/edit - Editar persona');

        $this->newLine();
        $this->info('✨ ¡Módulo de Gestión de Personas configurado correctamente!');
        $this->newLine();

        // Mostrar próximos pasos
        $this->info('📋 Próximos pasos recomendados:');
        $this->line('  1. Acceder a /admin/personas para ver el listado');
        $this->line('  2. Crear nuevas personas usando el formulario');
        $this->line('  3. Probar la búsqueda avanzada con diferentes filtros');
        $this->line('  4. Asignar líderes 1x10 a personas');
        $this->line('  5. Crear usuarios del sistema para líderes');
        $this->line('  6. Implementar funcionalidades de exportación/importación');

        return Command::SUCCESS;
    }

    private function checkTable(string $name, int $count): void
    {
        $this->line("  ✅ Tabla {$name}: {$count} registros");
    }
}
