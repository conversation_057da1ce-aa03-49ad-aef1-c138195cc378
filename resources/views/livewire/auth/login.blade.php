<div class="flex flex-col gap-6">
    <x-auth-header title="{{ __('global.log_into_your_account') }}" description="{{ __('global.log_into_your_account_text') }}" />

    <!-- Session Status -->
    <x-auth-session-status class="text-center" :status="session('status')" />

    <form wire:submit="login" class="flex flex-col gap-6">
        <!-- Username -->
        <flux:input
            wire:model="username"
            :label="__('global.username')"
            type="text"
            name="username"
            required
            autofocus
            autocomplete="username"
            placeholder="{{ __('global.username_placeholder') }}"
        />

        <!-- Password -->
        <div class="relative">
            <flux:input
                wire:model="password"
                :label="__('global.password')"
                type="password"
                name="password"
                required
                autocomplete="current-password"
                placeholder="Password"
            />

            @if (Route::has('password.request'))
                <flux:link class="absolute right-0 top-0 text-sm" :href="route('password.request')" wire:navigate>
                    {{ __('global.forgot_password') }}
                </flux:link>
            @endif
        </div>

        <!-- Remember Me -->
        <flux:checkbox wire:model="remember" :label="__('global.remember_me')" />

        <div class="flex items-center justify-end">
            <flux:button variant="primary" type="submit" class="w-full">{{ __('global.log_in') }}</flux:button>
        </div>
    </form>

    @if (Route::has('register'))
      <div class="space-x-1 text-center text-sm text-zinc-600 dark:text-zinc-400">
        <span>{{ __('global.dont_have_an_account') }}</span>
          <flux:link :href="route('register')" wire:navigate>
            {{ __('global.sign_up') }}
          </flux:link>
      </div>
    @endif
</div>
