<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON><PERSON>\Permission\Models\Permission;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [

            'access dashboard',

            'impersonate',

            'view users',
            'create users',
            'update users',
            'delete users',

            'view roles',
            'create roles',
            'update roles',
            'delete roles',

            'view permissions',
            'create permissions',
            'update permissions',
            'delete permissions',

            // Permisos para gestión de personas
            'view personas',
            'create personas',
            'update personas',
            'delete personas',
            'export personas',
            'import personas',
            'assign lider personas',
            'create user from persona',

            // Permisos para ubicaciones geográficas
            'view ubicaciones',
            'create ubicaciones',
            'update ubicaciones',
            'delete ubicaciones',

            // Permisos para eventos electorales
            'view eventos_electorales',
            'create eventos_electorales',
            'update eventos_electorales',
            'delete eventos_electorales',

            // Permisos para movilizaciones
            'view movilizaciones',
            'create movilizaciones',
            'update movilizaciones',
            'delete movilizaciones',
        ];

        foreach ($permissions as $permission) {
            Permission::query()->updateOrCreate([
                'name' => $permission,
            ]);
        }

    }
}
