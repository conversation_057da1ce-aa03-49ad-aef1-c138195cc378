<?php

namespace Database\Seeders;

use App\Models\Persona;
use App\Models\Estado;
use App\Models\Municipio;
use App\Models\Parroquia;
use App\Models\CentroVotacion;
use Illuminate\Database\Seeder;

class PersonasSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Obtener ubicaciones para asignar a las personas
        $estados = Estado::with(['municipios.parroquias.centrosVotacion'])->get();
        
        if ($estados->isEmpty()) {
            $this->command->warn('No hay estados disponibles. Ejecute primero UbicacionesSeeder.');
            return;
        }

        // Crear algunos líderes 1x10
        $lideres = [
            [
                'nombres' => '<PERSON>',
                'apellidos' => '<PERSON>',
                'cedula' => 'V-12345678',
                'fecha_nacimiento' => '1980-05-15',
                'genero' => 'F',
                'telefono' => '0414-1234567',
                'email' => '<EMAIL>',
                'tipo_persona' => 'militante',
                'es_lider_1x10' => true,
                'estado' => 'activo',
            ],
            [
                'nombres' => '<PERSON> <PERSON>',
                'apellidos' => '<PERSON>',
                'cedula' => 'V-23456789',
                'fecha_nacimiento' => '1975-08-22',
                'genero' => 'M',
                'telefono' => '0424-2345678',
                'email' => '<EMAIL>',
                'tipo_persona' => 'militante',
                'es_lider_1x10' => true,
                'estado' => 'activo',
            ],
            [
                'nombres' => 'Ana Beatriz',
                'apellidos' => 'López Silva',
                'cedula' => 'V-34567890',
                'fecha_nacimiento' => '1985-12-03',
                'genero' => 'F',
                'telefono' => '0412-3456789',
                'email' => '<EMAIL>',
                'tipo_persona' => 'militante',
                'es_lider_1x10' => true,
                'estado' => 'activo',
            ],
        ];

        $lideresCreados = [];
        foreach ($lideres as $liderData) {
            $ubicacion = $this->getRandomUbicacion($estados);
            $liderData = array_merge($liderData, $ubicacion);
            $lideresCreados[] = Persona::create($liderData);
        }

        // Crear personas regulares y asignarlas a líderes
        $personas = [
            [
                'nombres' => 'José Luis',
                'apellidos' => 'Hernández Castro',
                'cedula' => 'V-45678901',
                'fecha_nacimiento' => '1990-03-10',
                'genero' => 'M',
                'telefono' => '0416-4567890',
                'email' => '<EMAIL>',
                'tipo_persona' => 'votante',
                'estado' => 'activo',
            ],
            [
                'nombres' => 'Carmen Rosa',
                'apellidos' => 'Díaz Morales',
                'cedula' => 'V-56789012',
                'fecha_nacimiento' => '1988-07-18',
                'genero' => 'F',
                'telefono' => '0414-5678901',
                'email' => '<EMAIL>',
                'tipo_persona' => 'votante',
                'estado' => 'activo',
            ],
            [
                'nombres' => 'Roberto Carlos',
                'apellidos' => 'Vargas Ruiz',
                'cedula' => 'V-67890123',
                'fecha_nacimiento' => '1992-11-25',
                'genero' => 'M',
                'telefono' => '0424-6789012',
                'tipo_persona' => 'simpatizante',
                'estado' => 'activo',
            ],
            [
                'nombres' => 'Luisa Fernanda',
                'apellidos' => 'Torres Jiménez',
                'cedula' => 'V-78901234',
                'fecha_nacimiento' => '1987-04-12',
                'genero' => 'F',
                'telefono' => '0412-7890123',
                'email' => '<EMAIL>',
                'tipo_persona' => 'militante',
                'estado' => 'activo',
            ],
            [
                'nombres' => 'Miguel Ángel',
                'apellidos' => 'Ramírez Soto',
                'cedula' => 'V-89012345',
                'fecha_nacimiento' => '1983-09-08',
                'genero' => 'M',
                'telefono' => '0416-8901234',
                'email' => '<EMAIL>',
                'tipo_persona' => 'votante',
                'estado' => 'activo',
            ],
            [
                'nombres' => 'Patricia Elena',
                'apellidos' => 'Mendoza Flores',
                'cedula' => 'V-90123456',
                'fecha_nacimiento' => '1991-01-30',
                'genero' => 'F',
                'telefono' => '0414-9012345',
                'tipo_persona' => 'votante',
                'estado' => 'activo',
            ],
            [
                'nombres' => 'Fernando José',
                'apellidos' => 'Castillo Vega',
                'cedula' => 'V-01234567',
                'fecha_nacimiento' => '1986-06-14',
                'genero' => 'M',
                'telefono' => '0424-0123456',
                'email' => '<EMAIL>',
                'tipo_persona' => 'militante',
                'estado' => 'activo',
            ],
            [
                'nombres' => 'Gabriela María',
                'apellidos' => 'Moreno Aguilar',
                'cedula' => 'V-12340567',
                'fecha_nacimiento' => '1989-10-05',
                'genero' => 'F',
                'telefono' => '0412-1234056',
                'tipo_persona' => 'simpatizante',
                'estado' => 'activo',
            ],
            [
                'nombres' => 'Andrés Felipe',
                'apellidos' => 'Guerrero Núñez',
                'cedula' => 'V-23450678',
                'fecha_nacimiento' => '1984-12-20',
                'genero' => 'M',
                'telefono' => '0416-2345067',
                'email' => '<EMAIL>',
                'tipo_persona' => 'votante',
                'estado' => 'activo',
            ],
            [
                'nombres' => 'Valentina Isabel',
                'apellidos' => 'Rojas Herrera',
                'cedula' => 'V-34560789',
                'fecha_nacimiento' => '1993-02-28',
                'genero' => 'F',
                'telefono' => '0414-3456078',
                'tipo_persona' => 'votante',
                'estado' => 'activo',
            ],
        ];

        foreach ($personas as $index => $personaData) {
            $ubicacion = $this->getRandomUbicacion($estados);
            $personaData = array_merge($personaData, $ubicacion);
            
            // Asignar a un líder (distribuir equitativamente)
            $liderIndex = $index % count($lideresCreados);
            $personaData['lider_asignado_id'] = $lideresCreados[$liderIndex]->id;
            
            Persona::create($personaData);
        }

        $this->command->info('Se crearon ' . count($lideres) . ' líderes 1x10 y ' . count($personas) . ' personas regulares.');
    }

    private function getRandomUbicacion($estados)
    {
        $estado = $estados->random();
        $municipio = $estado->municipios->random();
        $parroquia = $municipio->parroquias->random();
        $centroVotacion = $parroquia->centrosVotacion->random();

        return [
            'estado_id' => $estado->id,
            'municipio_id' => $municipio->id,
            'parroquia_id' => $parroquia->id,
            'centro_votacion_id' => $centroVotacion->id,
            'mesa_votacion' => str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT),
            'direccion' => 'Dirección de ejemplo en ' . $parroquia->nombre . ', ' . $municipio->nombre,
        ];
    }
}
