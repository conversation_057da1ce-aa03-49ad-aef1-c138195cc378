<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Super Admin con todos los permisos
        $superAdmin = Role::query()->updateOrCreate(['name' => 'Super Admin']);
        $permissions = Permission::all()->pluck('name')->toArray();
        $superAdmin->givePermissionTo($permissions);

        // Coordinador de Personas - Gestión completa de personas
        $coordinador = Role::query()->updateOrCreate(['name' => 'Coordinador de Personas']);
        $coordinador->givePermissionTo([
            'access dashboard',
            'view personas',
            'create personas',
            'update personas',
            'delete personas',
            'export personas',
            'import personas',
            'assign lider personas',
            'create user from persona',
            'view ubicaciones',
            'view eventos_electorales',
            'view movilizaciones',
        ]);

        // Líder 1x10 - Gestión limitada de su grupo
        $lider1x10 = Role::query()->updateOrCreate(['name' => 'Líder 1x10']);
        $lider1x10->givePermissionTo([
            'access dashboard',
            'view personas', // Solo las de su grupo
            'update personas', // Solo las de su grupo
            'view eventos_electorales',
            'view movilizaciones',
        ]);

        // Militante - Acceso básico
        $militante = Role::query()->updateOrCreate(['name' => 'Militante']);
        $militante->givePermissionTo([
            'access dashboard',
            'view eventos_electorales',
            'view movilizaciones',
        ]);

        // Votante - Acceso muy limitado
        $votante = Role::query()->updateOrCreate(['name' => 'Votante']);
        $votante->givePermissionTo([
            'access dashboard',
        ]);

        // Operador de Datos - Para entrada de información
        $operador = Role::query()->updateOrCreate(['name' => 'Operador de Datos']);
        $operador->givePermissionTo([
            'access dashboard',
            'view personas',
            'create personas',
            'update personas',
            'view ubicaciones',
        ]);
    }
}
