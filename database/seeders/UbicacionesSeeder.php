<?php

namespace Database\Seeders;

use App\Models\Estado;
use App\Models\Municipio;
use App\Models\Parroquia;
use App\Models\CentroVotacion;
use Illuminate\Database\Seeder;

class UbicacionesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Crear algunos estados de ejemplo (Venezuela)
        $estados = [
            ['nombre' => 'Distrito Capital', 'codigo' => 'DC'],
            ['nombre' => 'Miranda', 'codigo' => 'MI'],
            ['nombre' => 'Carabobo', 'codigo' => 'CA'],
            ['nombre' => 'Zulia', 'codigo' => 'ZU'],
            ['nombre' => 'Aragua', 'codigo' => 'AR'],
        ];

        foreach ($estados as $estadoData) {
            $estado = Estado::create($estadoData);

            // Crear municipios para cada estado
            $municipios = $this->getMunicipiosPorEstado($estado->codigo);
            
            foreach ($municipios as $municipioData) {
                $municipio = $estado->municipios()->create($municipioData);

                // Crear parroquias para cada municipio
                $parroquias = $this->getParroquiasPorMunicipio($municipio->codigo);
                
                foreach ($parroquias as $parroquiaData) {
                    $parroquia = $municipio->parroquias()->create($parroquiaData);

                    // Crear centros de votación para cada parroquia
                    $centros = $this->getCentrosPorParroquia($parroquia->codigo);
                    
                    foreach ($centros as $centroData) {
                        $parroquia->centrosVotacion()->create($centroData);
                    }
                }
            }
        }
    }

    private function getMunicipiosPorEstado(string $codigoEstado): array
    {
        $municipios = [
            'DC' => [
                ['nombre' => 'Libertador', 'codigo' => 'LIB'],
            ],
            'MI' => [
                ['nombre' => 'Baruta', 'codigo' => 'BAR'],
                ['nombre' => 'Chacao', 'codigo' => 'CHA'],
                ['nombre' => 'El Hatillo', 'codigo' => 'HAT'],
                ['nombre' => 'Sucre', 'codigo' => 'SUC'],
            ],
            'CA' => [
                ['nombre' => 'Valencia', 'codigo' => 'VAL'],
                ['nombre' => 'Puerto Cabello', 'codigo' => 'PCA'],
            ],
            'ZU' => [
                ['nombre' => 'Maracaibo', 'codigo' => 'MAR'],
                ['nombre' => 'San Francisco', 'codigo' => 'SFR'],
            ],
            'AR' => [
                ['nombre' => 'Girardot', 'codigo' => 'GIR'],
                ['nombre' => 'Libertador', 'codigo' => 'LIB'],
            ],
        ];

        return $municipios[$codigoEstado] ?? [];
    }

    private function getParroquiasPorMunicipio(string $codigoMunicipio): array
    {
        $parroquias = [
            'LIB' => [
                ['nombre' => 'Catedral', 'codigo' => 'CAT'],
                ['nombre' => 'San Juan', 'codigo' => 'SJU'],
                ['nombre' => 'Santa Teresa', 'codigo' => 'STE'],
            ],
            'BAR' => [
                ['nombre' => 'Baruta', 'codigo' => 'BAR'],
                ['nombre' => 'El Cafetal', 'codigo' => 'CAF'],
            ],
            'CHA' => [
                ['nombre' => 'Chacao', 'codigo' => 'CHA'],
            ],
            'HAT' => [
                ['nombre' => 'El Hatillo', 'codigo' => 'HAT'],
            ],
            'SUC' => [
                ['nombre' => 'Petare', 'codigo' => 'PET'],
                ['nombre' => 'Caucagüita', 'codigo' => 'CAU'],
            ],
            'VAL' => [
                ['nombre' => 'Valencia', 'codigo' => 'VAL'],
                ['nombre' => 'Miguel Peña', 'codigo' => 'MPE'],
            ],
            'PCA' => [
                ['nombre' => 'Puerto Cabello', 'codigo' => 'PCA'],
            ],
            'MAR' => [
                ['nombre' => 'Maracaibo', 'codigo' => 'MAR'],
                ['nombre' => 'Cacique Mara', 'codigo' => 'CMA'],
            ],
            'SFR' => [
                ['nombre' => 'San Francisco', 'codigo' => 'SFR'],
            ],
            'GIR' => [
                ['nombre' => 'Maracay', 'codigo' => 'MAC'],
                ['nombre' => 'San Jacinto', 'codigo' => 'SJA'],
            ],
        ];

        return $parroquias[$codigoMunicipio] ?? [];
    }

    private function getCentrosPorParroquia(string $codigoParroquia): array
    {
        return [
            [
                'nombre' => 'Escuela Básica ' . $codigoParroquia . ' 001',
                'codigo' => $codigoParroquia . '001',
                'direccion' => 'Dirección de ejemplo para centro ' . $codigoParroquia . '001',
            ],
            [
                'nombre' => 'Liceo ' . $codigoParroquia . ' 002',
                'codigo' => $codigoParroquia . '002',
                'direccion' => 'Dirección de ejemplo para centro ' . $codigoParroquia . '002',
            ],
        ];
    }
}
