<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('participaciones_movilizacion', function (Blueprint $table) {
            $table->id();
            $table->foreignId('persona_id')->constrained('personas')->onDelete('cascade');
            $table->foreignId('movilizacion_id')->constrained('movilizaciones')->onDelete('cascade');
            $table->enum('estado_participacion', ['invitado', 'confirmado', 'asistio', 'no_asistio'])->default('invitado');
            $table->datetime('fecha_confirmacion')->nullable();
            $table->datetime('fecha_asistencia')->nullable();
            $table->text('observaciones')->nullable();
            $table->timestamps();

            $table->unique(['persona_id', 'movilizacion_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('participaciones_movilizacion');
    }
};
