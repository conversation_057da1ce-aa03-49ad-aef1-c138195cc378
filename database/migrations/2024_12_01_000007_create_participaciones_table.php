<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('participaciones', function (Blueprint $table) {
            $table->id();
            $table->foreignId('persona_id')->constrained('personas')->onDelete('cascade');
            $table->foreignId('evento_electoral_id')->constrained('eventos_electorales')->onDelete('cascade');
            $table->enum('tipo_participacion', ['voto', 'testigo', 'coordinador', 'movilizador'])->default('voto');
            $table->boolean('confirmo_participacion')->default(false);
            $table->datetime('fecha_confirmacion')->nullable();
            $table->text('observaciones')->nullable();
            $table->timestamps();

            $table->unique(['persona_id', 'evento_electoral_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('participaciones');
    }
};
