<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('movilizaciones', function (Blueprint $table) {
            $table->id();
            $table->string('nombre');
            $table->text('descripcion')->nullable();
            $table->datetime('fecha_inicio');
            $table->datetime('fecha_fin')->nullable();
            $table->enum('tipo', ['evento', 'campana', 'reunion', 'capacitacion'])->default('evento');
            $table->enum('estado', ['planificada', 'en_curso', 'finalizada', 'cancelada'])->default('planificada');
            $table->text('ubicacion')->nullable();
            $table->integer('meta_participantes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('movilizaciones');
    }
};
