<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('personas', function (Blueprint $table) {
            $table->id();
            
            // Datos personales
            $table->string('nombres');
            $table->string('apellidos');
            $table->string('cedula', 20)->unique();
            $table->date('fecha_nacimiento')->nullable();
            $table->enum('genero', ['M', 'F', 'O'])->nullable();
            
            // Información de contacto
            $table->string('telefono', 20)->nullable();
            $table->string('telefono_secundario', 20)->nullable();
            $table->string('email')->nullable();
            $table->text('direccion')->nullable();
            
            // Ubicación geográfica
            $table->foreignId('estado_id')->nullable()->constrained('estados')->onDelete('set null');
            $table->foreignId('municipio_id')->nullable()->constrained('municipios')->onDelete('set null');
            $table->foreignId('parroquia_id')->nullable()->constrained('parroquias')->onDelete('set null');
            
            // Información electoral
            $table->foreignId('centro_votacion_id')->nullable()->constrained('centros_votacion')->onDelete('set null');
            $table->string('mesa_votacion', 10)->nullable();
            
            // Rol en el sistema
            $table->enum('tipo_persona', ['militante', 'votante', 'simpatizante'])->default('votante');
            $table->boolean('es_lider_1x10')->default(false);
            $table->foreignId('lider_asignado_id')->nullable()->constrained('personas')->onDelete('set null');
            
            // Relación con usuario del sistema
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            
            // Estado y metadatos
            $table->enum('estado', ['activo', 'inactivo', 'suspendido'])->default('activo');
            $table->text('observaciones')->nullable();
            $table->json('datos_adicionales')->nullable();
            
            $table->timestamps();
            
            // Índices para optimizar búsquedas
            $table->index(['nombres', 'apellidos']);
            $table->index(['cedula']);
            $table->index(['tipo_persona']);
            $table->index(['es_lider_1x10']);
            $table->index(['estado']);
            $table->index(['estado_id', 'municipio_id', 'parroquia_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('personas');
    }
};
