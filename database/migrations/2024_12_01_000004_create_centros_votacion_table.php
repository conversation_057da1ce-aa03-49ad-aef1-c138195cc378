<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('centros_votacion', function (Blueprint $table) {
            $table->id();
            $table->foreignId('parroquia_id')->constrained('parroquias')->onDelete('cascade');
            $table->string('nombre');
            $table->string('codigo', 20)->unique();
            $table->text('direccion')->nullable();
            $table->boolean('activo')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('centros_votacion');
    }
};
