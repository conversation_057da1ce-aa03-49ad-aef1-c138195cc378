# Módulo de Gestión de Personas

## Descripción

El módulo de Gestión de Personas es un sistema completo para administrar toda la información relacionada con las personas registradas en el sistema, implementando la metodología 1x10 donde un líder puede gestionar hasta 10 personas.

## Características Principales

### ✅ Funcionalidades Implementadas

1. **Listado de Personas**
   - Vista tabular con filtros avanzados
   - Pestañas por tipo: Todos, Militantes, Votantes, Simpatizantes, Líderes 1x10
   - Búsqueda en tiempo real
   - Paginación optimizada

2. **Registro de Personas**
   - Formulario completo con validaciones
   - Datos personales (nombre, cédula, fecha de nacimiento, género)
   - Información de contacto (teléfonos, email, dirección)
   - Ubicación geográfica (estado, municipio, parroquia)
   - Información electoral (centro de votación, mesa)
   - Asignación de roles y liderazgo 1x10

3. **Detalles de Persona**
   - Vista completa de información personal
   - Historial de participación en eventos electorales
   - Estado en movilizaciones
   - Gestión de asignación como líder 1x10
   - Creación de cuenta de usuario
   - Visualización de personas asignadas (si es líder)

4. **Búsqueda Avanzada**
   - Múltiples criterios de búsqueda
   - Filtros por ubicación geográfica
   - Filtros por clasificación y estado
   - Filtros por fechas y rangos de edad
   - Exportación de resultados

5. **Sistema de Roles y Permisos**
   - Super Admin: Acceso completo
   - Coordinador de Personas: Gestión completa de personas
   - Líder 1x10: Gestión limitada de su grupo
   - Militante: Acceso básico
   - Votante: Acceso muy limitado
   - Operador de Datos: Entrada de información

## Estructura de Base de Datos

### Tablas Principales

1. **estados** - Estados/provincias
2. **municipios** - Municipios por estado
3. **parroquias** - Parroquias por municipio
4. **centros_votacion** - Centros de votación por parroquia
5. **personas** - Información principal de personas
6. **eventos_electorales** - Eventos electorales
7. **participaciones** - Participación en eventos electorales
8. **movilizaciones** - Eventos de movilización
9. **participaciones_movilizacion** - Participación en movilizaciones

### Modelo de Datos Persona

```php
// Datos personales
'nombres', 'apellidos', 'cedula', 'fecha_nacimiento', 'genero'

// Información de contacto
'telefono', 'telefono_secundario', 'email', 'direccion'

// Ubicación geográfica
'estado_id', 'municipio_id', 'parroquia_id'

// Información electoral
'centro_votacion_id', 'mesa_votacion'

// Rol en el sistema
'tipo_persona', 'es_lider_1x10', 'lider_asignado_id'

// Relación con usuario
'user_id'

// Estado y metadatos
'estado', 'observaciones', 'datos_adicionales'
```

## Rutas Disponibles

```php
// Gestión de Personas
GET /admin/personas                    - Listado de personas
GET /admin/personas/create             - Crear persona
GET /admin/personas/busqueda-avanzada  - Búsqueda avanzada
GET /admin/personas/{persona}          - Ver detalles
GET /admin/personas/{persona}/edit     - Editar persona
```

## Componentes Livewire

1. **App\Livewire\Admin\Personas** - Listado principal
2. **App\Livewire\Admin\Personas\CreatePersona** - Crear persona
3. **App\Livewire\Admin\Personas\EditPersona** - Editar persona
4. **App\Livewire\Admin\Personas\ViewPersona** - Ver detalles
5. **App\Livewire\Admin\Personas\BusquedaAvanzada** - Búsqueda avanzada

## Modelos Principales

1. **App\Models\Persona** - Modelo principal
2. **App\Models\Estado** - Estados/provincias
3. **App\Models\Municipio** - Municipios
4. **App\Models\Parroquia** - Parroquias
5. **App\Models\CentroVotacion** - Centros de votación
6. **App\Models\EventoElectoral** - Eventos electorales
7. **App\Models\Movilizacion** - Movilizaciones

## Metodología 1x10

### Concepto
Un líder 1x10 puede gestionar hasta 10 personas bajo su responsabilidad.

### Implementación
- Campo `es_lider_1x10` en la tabla personas
- Campo `lider_asignado_id` para la relación líder-seguidor
- Validaciones para no exceder el límite de 10 personas por líder
- Métodos en el modelo para verificar espacios disponibles

### Funcionalidades
- Asignación automática de personas a líderes
- Visualización de personas asignadas
- Conteo de espacios disponibles
- Restricciones para eliminar líderes con personas asignadas

## Permisos del Sistema

### Permisos Específicos de Personas
- `view personas` - Ver listado de personas
- `create personas` - Crear nuevas personas
- `update personas` - Editar personas existentes
- `delete personas` - Eliminar personas
- `export personas` - Exportar datos
- `import personas` - Importar datos
- `assign lider personas` - Asignar líderes
- `create user from persona` - Crear usuarios del sistema

## Traducciones

El módulo incluye traducciones completas en:
- **Español** (`lang/es/personas.php`)
- **Inglés** (`lang/en/personas.php`)

## Validaciones Implementadas

### Datos Únicos
- Cédula única por persona
- Email único (opcional)

### Validaciones de Negocio
- Una persona no puede ser líder de sí misma
- Un líder no puede tener más de 10 personas asignadas
- No se puede eliminar un líder con personas asignadas
- Validaciones de fechas y formatos

### Validaciones de Formulario
- Campos requeridos marcados apropiadamente
- Validación de formatos (email, teléfono, cédula)
- Validación de fechas (fecha de nacimiento anterior a hoy)

## Próximas Funcionalidades

### 🔄 En Desarrollo
1. **Exportación/Importación**
   - Exportar a Excel, PDF, CSV
   - Importar desde archivos Excel/CSV
   - Plantillas de importación

2. **Reportes Avanzados**
   - Reportes estadísticos
   - Gráficos de distribución
   - Reportes por ubicación geográfica

3. **Notificaciones**
   - Notificaciones automáticas
   - Alertas por email
   - Recordatorios de eventos

4. **API REST**
   - Endpoints para integración
   - Autenticación API
   - Documentación Swagger

## Instalación y Configuración

### Requisitos
- Laravel 11+
- Livewire 3.0+
- Spatie Laravel Permission
- Flux UI Components

### Pasos de Instalación

1. **Ejecutar migraciones**
   ```bash
   php artisan migrate
   ```

2. **Ejecutar seeders**
   ```bash
   php artisan db:seed --class=UbicacionesSeeder
   php artisan db:seed --class=RoleSeeder
   ```

3. **Verificar instalación**
   ```bash
   php artisan test:personas-module
   ```

### Configuración Inicial

1. **Asignar permisos a usuarios**
   ```php
   $user->assignRole('Coordinador de Personas');
   ```

2. **Crear datos de ubicación**
   - Ejecutar el seeder de ubicaciones
   - O crear manualmente estados, municipios, parroquias

3. **Configurar roles según necesidades**
   - Ajustar permisos por rol
   - Crear roles adicionales si es necesario

## Uso del Sistema

### Para Coordinadores
1. Acceder a `/admin/personas`
2. Usar filtros para encontrar personas específicas
3. Crear nuevas personas con el botón "Agregar Persona"
4. Usar búsqueda avanzada para consultas complejas
5. Asignar líderes 1x10 según sea necesario

### Para Líderes 1x10
1. Ver su grupo de personas asignadas
2. Actualizar información de contacto
3. Reportar participación en eventos

### Para Operadores de Datos
1. Registrar nuevas personas
2. Actualizar información existente
3. Verificar datos de ubicación

## Soporte y Mantenimiento

### Logs del Sistema
- Todas las acciones se registran en logs de Laravel
- Usar `php artisan log:clear` para limpiar logs

### Backup de Datos
- Respaldar regularmente la base de datos
- Especial atención a las tablas de personas y ubicaciones

### Monitoreo
- Verificar integridad de datos regularmente
- Monitorear asignaciones de líderes 1x10
- Revisar usuarios sin actividad

## Contacto y Soporte

Para soporte técnico o consultas sobre el módulo:
- Revisar la documentación completa
- Verificar logs de errores
- Contactar al equipo de desarrollo

---

**Versión:** 1.0.0  
**Última actualización:** Diciembre 2024  
**Desarrollado para:** Sistema de Gestión Electoral con metodología 1x10
